# Prompt Clustering Report

Generated by `cluster_prompts.py` – 2025-04-16


## Overview

* Total prompts: **213**
* Clustering method: **kmeans**
* k (K‑Means): **2**
* Silhouette score: **0.042**
* Final clusters (excluding noise): **2**


| label | name | #prompts | description |
|-------|------|---------:|-------------|
| 0 | Creative Guidance Roles | 121 | This cluster encompasses a variety of roles where individuals provide expert advice, suggestions, and creative ideas across different fields. Each role, be it interior decorator, comedian, IT architect, or artist advisor, focuses on enhancing the expertise and creativity of others by tailoring advice to specific requests and contexts. |
| 1 | Role Customization Requests | 92 | This cluster contains various requests for role-specific assistance across different domains, including web development, language processing, IT troubleshooting, and creative endeavors. Each snippet illustrates a unique role that a user wishes to engage with, focusing on specific tasks without requiring explanations. |

---
## Plots

The directory `plots/` contains a bar chart of the cluster sizes and a t‑SNE scatter plot coloured by cluster.
