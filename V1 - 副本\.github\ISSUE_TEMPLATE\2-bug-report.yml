name: 🪲 Bug Report
description: Report an issue that should be fixed
labels:
  - bug
  - needs triage
body:
  - type: markdown
    attributes:
      value: |
        Thank you for submitting a bug report! It helps make Codex better for everyone.

        If you need help or support using Codex, and are not reporting a bug, please post on [codex/discussions](https://github.com/openai/codex/discussions), where you can ask questions or engage with others on ideas for how to improve codex.

        Make sure you are running the [latest](https://npmjs.com/package/@openai/codex) version of Codex CLI. The bug you are experiencing may already have been fixed.

        Please try to include as much information as possible.

  - type: input
    id: version
    attributes:
      label: What version of Codex is running?
      description: Copy the output of `codex --version`
  - type: input
    id: model
    attributes:
      label: Which model were you using?
      description: Like `gpt-4.1`, `o4-mini`, `o3`, etc.
  - type: input
    id: platform
    attributes:
      label: What platform is your computer?
      description: |
        For MacOS and Linux: copy the output of `uname -mprs`
        For Windows: copy the output of `"$([Environment]::OSVersion | ForEach-Object VersionString) $(if ([Environment]::Is64BitOperatingSystem) { "x64" } else { "x86" })"` in the PowerShell console
  - type: textarea
    id: steps
    attributes:
      label: What steps can reproduce the bug?
      description: Explain the bug and provide a code snippet that can reproduce it.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What is the expected behavior?
      description: If possible, please provide text instead of a screenshot.
  - type: textarea
    id: actual
    attributes:
      label: What do you see instead?
      description: If possible, please provide text instead of a screenshot.
  - type: textarea
    id: notes
    attributes:
      label: Additional information
      description: Is there anything else you think we should know?
