[workspace]
resolver = "2"
members = [
    "ansi-escape",
    "apply-patch",
    "cli",
    "common",
    "core",
    "exec",
    "execpolicy",
    "mcp-client",
    "mcp-server",
    "mcp-types",
    "tui",
]

[workspace.package]
version = "0.0.0"
# Track the edition for all workspace crates in one place. Individual
# crates can still override this value, but keeping it here means new
# crates created with `cargo new -w ...` automatically inherit the 2024
# edition.
edition = "2024"

[workspace.lints]
rust = { }

[workspace.lints.clippy]
expect_used = "deny"
unwrap_used = "deny"

[profile.release]
lto = "fat"
# Because we bundle some of these executables with the TypeScript CLI, we
# remove everything to make the binary as small as possible.
strip = "symbols"
