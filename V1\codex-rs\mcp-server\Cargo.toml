[package]
name = "codex-mcp-server"
version = { workspace = true }
edition = "2024"

[[bin]]
name = "codex-mcp-server"
path = "src/main.rs"

[lib]
name = "codex_mcp_server"
path = "src/lib.rs"

[lints]
workspace = true

[dependencies]
codex-core = { path = "../core" }
mcp-types = { path = "../mcp-types" }
schemars = "0.8.22"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tracing = { version = "0.1.41", features = ["log"] }
tracing-subscriber = { version = "0.3", features = ["fmt", "env-filter"] }
tokio = { version = "1", features = [
    "io-std",
    "macros",
    "process",
    "rt-multi-thread",
    "signal",
] }

[dev-dependencies]
pretty_assertions = "1.4.1"
