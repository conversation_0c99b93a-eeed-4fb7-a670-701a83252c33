[package]
name = "codex-execpolicy"
version = { workspace = true }
edition = "2024"

[[bin]]
name = "codex-execpolicy"
path = "src/main.rs"

[lib]
name = "codex_execpolicy"
path = "src/lib.rs"

[lints]
workspace = true

[dependencies]
anyhow = "1"
starlark = "0.13.0"
allocative = "0.3.3"
clap = { version = "4", features = ["derive"] }
derive_more = { version = "1", features = ["display"] }
env_logger = "0.11.5"
log = "0.4"
multimap = "0.10.0"
path-absolutize = "3.1.1"
regex = "1.11.1"
serde = { version = "1.0.194", features = ["derive"] }
serde_json = "1.0.110"
serde_with = { version = "3", features = ["macros"] }
tempfile = "3.13.0"
