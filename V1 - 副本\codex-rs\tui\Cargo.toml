[package]
name = "codex-tui"
version = { workspace = true }
edition = "2024"

[[bin]]
name = "codex-tui"
path = "src/main.rs"

[lib]
name = "codex_tui"
path = "src/lib.rs"

[lints]
workspace = true

[dependencies]
anyhow = "1"
clap = { version = "4", features = ["derive"] }
codex-ansi-escape = { path = "../ansi-escape" }
codex-core = { path = "../core" }
codex-common = { path = "../common", features = ["cli", "elapsed"] }
color-eyre = "0.6.3"
crossterm = { version = "0.28.1", features = ["bracketed-paste"] }
lazy_static = "1"
mcp-types = { path = "../mcp-types" }
path-clean = "1.0.1"
ratatui = { version = "0.29.0", features = [
    "unstable-widget-ref",
    "unstable-rendered-line-info",
] }
regex = "1"
serde_json = "1"
shlex = "1.3.0"
strum = "0.27.1"
strum_macros = "0.27.1"
tokio = { version = "1", features = [
    "io-std",
    "macros",
    "process",
    "rt-multi-thread",
    "signal",
] }
tracing = { version = "0.1.41", features = ["log"] }
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
tui-input = "0.11.1"
tui-markdown = "0.3.3"
tui-textarea = "0.7.0"
uuid = "1"

[dev-dependencies]
pretty_assertions = "1"
