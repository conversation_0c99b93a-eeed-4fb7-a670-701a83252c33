{"nodes": {"flake-utils": {"inputs": {"systems": "systems"}, "locked": {"lastModified": 1731533236, "narHash": "sha256-l0KFg5HjrsfsO/JpG+r7fRrqm12kzFHyUHqHCVpMMbI=", "owner": "numtide", "repo": "flake-utils", "rev": "11707dc2f618dd54ca8739b309ec4fc024de578b", "type": "github"}, "original": {"owner": "numtide", "repo": "flake-utils", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1744463964, "narHash": "sha256-LWqduOgLHCFxiTNYi3Uj5Lgz0SR+Xhw3kr/3Xd0GPTM=", "owner": "NixOS", "repo": "nixpkgs", "rev": "2631b0b7abcea6e640ce31cd78ea58910d31e650", "type": "github"}, "original": {"owner": "NixOS", "ref": "nixos-unstable", "repo": "nixpkgs", "type": "github"}}, "root": {"inputs": {"flake-utils": "flake-utils", "nixpkgs": "nixpkgs", "rust-overlay": "rust-overlay"}}, "rust-overlay": {"inputs": {"nixpkgs": ["nixpkgs"]}, "locked": {"lastModified": 1746844454, "narHash": "sha256-GcUWDQUDRYrD34ol90KGUpjbVcOfUNbv0s955jPecko=", "owner": "oxalica", "repo": "rust-overlay", "rev": "be092436d4c0c303b654e4007453b69c0e33009e", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}, "systems": {"locked": {"lastModified": 1681028828, "narHash": "sha256-Vy1rq5AaRuLzOxct8nz4T6wlgyUR7zLU309k9mBC768=", "owner": "nix-systems", "repo": "default", "rev": "da67096a3b9bf56a91d16901293e51ba5b49a27e", "type": "github"}, "original": {"owner": "nix-systems", "repo": "default", "type": "github"}}}, "root": "root", "version": 7}